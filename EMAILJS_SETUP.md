# EmailJS Setup Guide - Tezz Designs

## Overview
The livery request form now uses EmailJS to send emails directly from the frontend. This is simpler than server-side email and requires no environment variables!

## Current Configuration
- **Service ID**: `service_tbnov0u` (already configured)
- **Developer Email**: `<EMAIL>` (your email)
- **Client Email**: `<EMAIL>` (receives all requests)

## Required Setup Steps

### Step 1: Get Your Public Key
1. Go to [EmailJS Dashboard](https://dashboard.emailjs.com/)
2. Sign in with your account (`<EMAIL>`)
3. Go to **Account** → **API Keys**
4. Copy your **Public Key**
5. Replace the placeholder in `pages/request-livery.tsx`:
   ```javascript
   emailjs.init("YOUR_PUBLIC_KEY_HERE"); // Line 35
   ```

### Step 2: Create Email Template
1. In EmailJS Dashboard, go to **Email Templates**
2. Click **Create New Template**
3. Template ID should be: `template_livery_request`
4. Use this template content:

```html
Subject: 🎨 New Livery Request from {{from_name}}

<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .header { background: linear-gradient(135deg, #f59e0b, #ea580c); color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .field { margin-bottom: 15px; }
        .label { font-weight: bold; color: #555; }
        .value { background: white; padding: 10px; border-left: 4px solid #f59e0b; margin-top: 5px; }
        .footer { background: #333; color: white; padding: 15px; text-align: center; font-size: 12px; }
        .price { background: #10b981; color: white; padding: 8px 16px; border-radius: 6px; font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎨 New Livery Request - Tezz Designs</h1>
        <p>Submitted on {{submission_date}}</p>
    </div>
    
    <div class="content">
        <h2>Customer Information</h2>
        <div class="field">
            <div class="label">Name:</div>
            <div class="value">{{from_name}}</div>
        </div>
        
        <div class="field">
            <div class="label">Email:</div>
            <div class="value">{{from_email}}</div>
        </div>
        
        <div class="field">
            <div class="label">Phone:</div>
            <div class="value">{{phone}}</div>
        </div>
        
        <h2>Livery Details</h2>
        <div class="field">
            <div class="label">Platform & Price:</div>
            <div class="value">{{platform}} - <span class="price">{{price}}</span></div>
        </div>
        
        <div class="field">
            <div class="label">Car Choice:</div>
            <div class="value">{{car_choice}}</div>
        </div>
        
        <div class="field">
            <div class="label">Body Kit:</div>
            <div class="value">{{body_kit}}</div>
        </div>
        
        <div class="field">
            <div class="label">Wheels:</div>
            <div class="value">{{wheels}}</div>
        </div>
        
        <div class="field">
            <div class="label">Design Style:</div>
            <div class="value">{{design_style}}</div>
        </div>
        
        <div class="field">
            <div class="label">Colors:</div>
            <div class="value">{{colors}}</div>
        </div>
        
        <div class="field">
            <div class="label">Additional Requests:</div>
            <div class="value">{{additional_requests}}</div>
        </div>
        
        <div class="field">
            <div class="label">Reference Images:</div>
            <div class="value">{{image_note}}</div>
        </div>
    </div>
    
    <div class="footer">
        <p>This request was submitted through the Tezz Designs website.</p>
        <p>Reply to {{from_email}} to follow up with the customer.</p>
        <p>💳 Payment Info: NZ Bank 02-0404-0278295-083 | PayPal: Kj.walker888@gmail.<NAME_EMAIL></p>
    </div>
</body>
</html>
```

### Step 3: Test the System
1. Run `npm run dev`
2. Go to `/request-livery`
3. Fill out and submit a test form
4. Check `<EMAIL>` for the email!

## Template Variables Used
The form automatically populates these variables:
- `{{from_name}}` - Customer name
- `{{from_email}}` - Customer email
- `{{phone}}` - Customer phone
- `{{platform}}` - Console or PC
- `{{price}}` - $20 USD or $35 USD
- `{{car_choice}}` - Car selection
- `{{body_kit}}` - Body kit choice
- `{{wheels}}` - Wheel preference
- `{{design_style}}` - Design style
- `{{colors}}` - Color preferences
- `{{additional_requests}}` - Extra requests
- `{{image_note}}` - Reference image info
- `{{submission_date}}` - When submitted

## Benefits of EmailJS
✅ **No server setup** required  
✅ **No environment variables** needed  
✅ **Frontend only** solution  
✅ **Free tier** available (200 emails/month)  
✅ **Direct to client email**  
✅ **Professional HTML emails**  

## File Attachments
EmailJS doesn't support file attachments directly. The form handles this by:
- Letting customers select reference images
- Noting in the email that images were selected
- Instructing you to contact the customer directly for images

## Troubleshooting
- **Emails not sending**: Check your Public Key
- **Template not found**: Ensure template ID is `template_livery_request`
- **Wrong recipient**: Email goes to `<EMAIL>`
- **Rate limits**: Free tier has 200 emails/month limit 