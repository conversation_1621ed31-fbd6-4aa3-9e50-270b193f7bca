# Tezz Designs Portfolio

A modern portfolio website for Tezz Designs showcasing premium CarX livery designs with an integrated order form.

## Features

- **Modern Design**: Beautiful gradient backgrounds and smooth animations
- **Portfolio Gallery**: Showcase livery designs (ready for image uploads)
- **Order Form**: Comprehensive form with all required fields for custom livery orders
- **Platform Selection**: Console ($20) vs PC ($35) pricing
- **Form Validation**: Complete form validation with error handling
- **Responsive Design**: Mobile-first responsive design
- **Accessibility**: Full accessibility support with ARIA labels and keyboard navigation

## Tech Stack

- **Framework**: Next.js 14 with TypeScript
- **Styling**: TailwindCSS
- **Form Handling**: React Hook Form
- **Deployment Ready**: Optimized for production

## Getting Started

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Run Development Server**
   ```bash
   npm run dev
   ```

3. **Open in Browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Project Structure

```
tezz-designs/
├── pages/
│   ├── _app.tsx          # App configuration
│   └── index.tsx         # Main portfolio page
├── styles/
│   └── globals.css       # Global styles with TailwindCSS
├── practices/            # Best practice documentation
└── config files...
```

## Form Features

The order form includes:
- Platform selection (Console/PC) with dynamic pricing
- Car specifications (model, body kit, wheels)
- Design requirements (style, colors, additional requests)
- File upload for reference images
- Contact information
- Terms agreement with copyright notice
- Full form validation

## Payment Information

- **Console Liveries**: $20 USD
- **PC Liveries**: $35 USD (More layers available)
- **Bank Transfer**: Available for New Zealand customers
- **PayPal**: Accepted worldwide

## Development Commands

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Type check with TypeScript

## Customization

The portfolio is ready for customization:
- Add livery images to the gallery section
- Integrate with backend for form submissions
- Add payment processing integration
- Customize colors and branding

## License

© 2025 Tezz Designs. All rights reserved. 