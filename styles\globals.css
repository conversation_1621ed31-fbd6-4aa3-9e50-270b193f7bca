@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* 3D Transform Utilities for Modern Gallery */
@layer utilities {
  .perspective-1000 {
    perspective: 1000px;
  }
  
  .perspective-1200 {
    perspective: 1200px;
  }
  
  .transform-style-preserve-3d {
    transform-style: preserve-3d;
  }
  
  .hover\:rotate-y-12:hover {
    transform: rotateY(12deg);
  }
  
  .hover\:rotate-y-6:hover {
    transform: rotateY(6deg);
  }
  
  .hover\:rotate-x-6:hover {
    transform: rotateX(6deg);
  }
  
  .backface-hidden {
    backface-visibility: hidden;
  }
  
  /* Custom shadows for enhanced depth */
  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
  }
  
  .shadow-4xl {
    box-shadow: 0 45px 80px -15px rgba(0, 0, 0, 0.3);
  }
  
  /* Enhanced backdrop blur */
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
  
  .backdrop-blur-4xl {
    backdrop-filter: blur(64px);
  }
  
  /* Glass morphism effects */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  .glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  /* Smooth animations */
  .animate-float-in {
    animation: floatIn 0.8s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.6s ease-out forwards;
  }

  /* Animation delays */
  .delay-200 {
    animation-delay: 0.2s;
  }

  .delay-400 {
    animation-delay: 0.4s;
  }

  .delay-500 {
    animation-delay: 0.5s;
  }

  .delay-600 {
    animation-delay: 0.6s;
  }

  .delay-1000 {
    animation-delay: 1s;
  }

  @keyframes floatIn {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Enhanced button hover effects */
  .button-glow {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
  }

  .button-glow:hover {
    box-shadow: 0 0 30px rgba(251, 191, 36, 0.5);
  }
  
  /* Enhanced hover states */
  .hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  }
} 