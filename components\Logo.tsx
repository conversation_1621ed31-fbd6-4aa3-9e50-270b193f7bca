import React from 'react';
import Image from 'next/image';

interface LogoProps {
  size?: 'small' | 'medium' | 'large' | 'hero';
  showText?: boolean;
  className?: string;
}

const Logo: React.FC<LogoProps> = ({ 
  size = 'medium', 
  showText = true, 
  className = '' 
}) => {
  const sizeClasses = {
    small: 'w-8 h-8',
    medium: 'w-14 h-14',
    large: 'w-24 h-24',
    hero: 'w-full h-full'
  };

  const textSizeClasses = {
    small: 'text-base',
    medium: 'text-xl',
    large: 'text-3xl',
    hero: 'text-5xl md:text-7xl'
  };

  if (size === 'hero') {
    return (
      <div className={`w-full ${className}`}>
        <div className={`relative ${sizeClasses[size]}`}>
          <Image
            src="/images/Untitled-1-1.png"
            alt="Tezz Designs Logo"
            fill
            className="object-contain"
            priority
            sizes="100vw"
          />
        </div>
        {showText && (
          <h1 className={`font-bold bg-gradient-to-r from-yellow-400 via-red-500 to-pink-500 bg-clip-text text-transparent text-center mt-4 ${textSizeClasses[size]}`}>
            TEZZ DESIGNS
          </h1>
        )}
      </div>
    );
  }

  return (
    <div className={`flex items-center justify-center gap-4 ${className}`}>
      <div className={`relative ${sizeClasses[size]} flex-shrink-0`}>
        <Image
          src="/images/Untitled-1-1.png"
          alt="Tezz Designs Logo"
          fill
          className="object-contain"
          priority
          sizes="100vw"
        />
      </div>
      {showText && (
        <h1 className={`font-bold bg-gradient-to-r from-yellow-400 via-red-500 to-pink-500 bg-clip-text text-transparent ${textSizeClasses[size]}`}>
          TEZZ DESIGNS
        </h1>
      )}
    </div>
  );
};

export default Logo; 