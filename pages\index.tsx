import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import Logo from '../components/Logo';
import Dock from '../components/Dock';
import ModernGallery from '../components/ModernGallery';
import Particles from '../components/Particles';
import { VscHome, VscArchive, VscAccount, VscSettingsGear } from 'react-icons/vsc';

const HomePage: React.FC = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768 || 'ontouchstart' in window);
    };
    
    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  const handleSeeMoreClick = () => {
    document.getElementById('livery-showcase')?.scrollIntoView({ 
      behavior: 'smooth' 
    });
  };

  // Navigation handlers for dock
  const handleNavigation = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Dock items configuration
  const dockItems = [
    {
      icon: <VscHome size={18} />,
      label: 'Home',
      onClick: () => handleNavigation('home')
    },
    {
      icon: <VscArchive size={18} />,
      label: 'Gallery',
      onClick: () => handleNavigation('livery-showcase')
    },
    {
      icon: <VscSettingsGear size={18} />,
      label: 'Services',
      onClick: () => handleNavigation('services')
    },
    {
      icon: <VscAccount size={18} />,
      label: 'About',
      onClick: () => handleNavigation('about')
    },
  ];

  // Array of all images from public/images (excluding logo)
  const liveryImages = [
    '/images/635260_139_upscayl_4x_realesrgan-x4plus-2.png',
    '/images/635260_20231112103023_1-1.jpg',
    '/images/635260_20231112124552_1-1-2.jpg',
    '/images/635260_20231113214312_1-2.jpg',
    '/images/635260_20231114204845_1-1-1.jpg',
    '/images/635260_20231203131657_1-1.jpg',
    '/images/635260_20231206184745_1-2.png',
    '/images/635260_20231209164618_1-2.jpg',
    '/images/635260_20231211212940_1-2.jpg',
    '/images/635260_20231212103921_1-1-1.jpg',
    '/images/635260_20231213131215_1-2.jpg',
    '/images/635260_20231217112824_1-2.jpg',
    '/images/635260_20240104170418_1-2.jpg',
    '/images/635260_20240105080320_1-1.png',
    '/images/635260_20240106182336_1-1.jpg',
    '/images/635260_20240110213143_1-2.png',
    '/images/635260_20240214002643_1-1-1.png',
    '/images/635260_20240217102735_1-1-1.png',
    '/images/635260_20240217123850_1-1.png',
    '/images/635260_20240221002523_1-3.png',
    '/images/635260_20240223223831_1-1.png',
    '/images/635260_20240224091442_1-1.png',
    '/images/635260_20240227215305_1-2.png',
    '/images/635260_20240229232057_1-2.png',
    '/images/635260_20240302095232_1-1.png',
    '/images/635260_20240306220552_1-2.png',
    '/images/635260_20240310092158_1-1-1.png',
    '/images/635260_20240310214719_1-1-2.png',
    '/images/635260_20240509214934_1-1-1.png',
    '/images/635260_20240510082255_1-2-2.png',
    '/images/635260_20240519105009_1-3-1.png',
    '/images/635260_20240613184925_1-1-2.png',
    '/images/635260_20240628181306_1-1-2.png',
    // '/images/635260_20240703224044_1-2-2.jpg',
    '/images/635260_20240715205959_1-2-1.png',
    '/images/635260_20240726213535_1-2-1.png',
    '/images/635260_20240814201236_1-1-2.png',
    '/images/635260_20240814201919_1-1-2.png',
    '/images/635260_20240827200535_1-1-1.png',
    '/images/635260_20240917202905_1-2-1.png',
    '/images/635260_20240923191853_1-1-2.png',
    '/images/635260_20241020191621_1-2-1.png',
    '/images/635260_20241102054607_1-1-2.png',
    '/images/635260_20241119180658_1-2.png',
    '/images/635260_20241129195926_1-1-2.png',
    '/images/635260_20241217201724_1-1-1.png',
    '/images/635260_20241217220908_1-2-1.png',
    '/images/635260_20241218084511_1-2-1.png',
    '/images/635260_20241219205549_1-1.png',
    '/images/635260_20241225210227_1-1-1.png',
    '/images/635260_20250111174540_1-1-1.png',
    '/images/635260_20250112115805_1-1-1.png',
    '/images/635260_20250112211245_1-1.png',
    '/images/635260_20250116200441_1-1.png',
    '/images/635260_31-2.jpg',
    '/images/635260_77-2.jpg',
    '/images/Drift_Racing_Online_2025-02-06_23-17-06-1.png',
    '/images/Drift_Racing_Online_2025-02-07_12-50-16-1.png',
    '/images/Drift_Racing_Online_2025-02-11_20-03-28-1.png',
    '/images/Drift_Racing_Online_2025-02-16_22-19-37-1.png',
    '/images/Drift_Racing_Online_2025-02-17_04-50-21-1.png',
    '/images/Drift_Racing_Online_2025-03-10_20-08-37-2.png',
    '/images/Drift_Racing_Online_2025-03-10_20-10-39-1.png',
    '/images/Drift_Racing_Online_2025-03-13_19-44-47-1.png',
    '/images/Drift_Racing_Online_2025-04-08_19-31-54-2.png',
    '/images/Drift_Racing_Online_2025-04-22_16-59-26-2.png',
    '/images/Drift_Racing_Online_2025-04-22_22-14-02-3.png',
    '/images/Drift_Racing_Online_2025-05-12_23-08-12-2.png',
    '/images/Drift_Racing_Online_2025-06-05_22-26-36.jpg',
    '/images/Drift_Racing_Online_2025-06-12_21-02-43-1.png',
    '/images/Drift_Racing_Online_2025-06-13_02-21-52-1.png',
    '/images/Drift_Racing_Online_2025-06-21_13-13-03-1.png',
    '/images/Drift_Racing_Online_2025-06-22_08-24-25-1.png',
    '/images/Drift_Racing_Online_2025-07-02_19-33-26-1.png',
    '/images/Drift_Racing_Online_2025-07-11_19-24-27-1.png',
    '/images/Drift_Racing_Online_2025-07-23_22-28-21-1.png',
    '/images/Drift_Racing_Online_2025-07-28_04-46-18-1.png',
    '/images/IMG_20231112_223135_078-1-2.jpg',
  ];

  // Convert images array to gallery items format
  const galleryItems = liveryImages.map((image) => ({ image }));

  return (
    <>
      <Head>
        <title>Tezz Designs - Premium CarX Livery Designs</title>
        <meta name="description" content="Professional CarX livery designs by Tezz. Custom automotive liveries and design services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      
      <main className="bg-black text-white relative">
        {/* Hero Section */}
        <section id="home" className="relative min-h-screen bg-black flex flex-col items-center overflow-hidden">
          {/* Particles Background */}
          <div className="absolute inset-0 w-full h-full">
            <Particles
              particleColors={['#ffffff', '#ffffff']}
              particleCount={1500}
              particleSpread={12}
              speed={0.1}
              particleBaseSize={100}
              moveParticlesOnHover={false}
              alphaParticles={false}
              disableRotation={false}
            />
          </div>

          {/* Logo at Top */}
          <div className="relative z-10 mt-8 mb-8 w-full max-w-2xl">
            <div className="relative h-[20vh] min-h-[150px] max-h-[220px]">
              <Logo size="hero" showText={false} className="w-full h-full" />
            </div>
          </div>

          {/* Content Container - Centered */}
          <div className="flex-1 flex flex-col justify-center">
            <div className="relative z-10 text-center max-w-4xl mx-auto px-6">
            {/* Main Heading - More Compact */}
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-4 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent animate-float-in">
              Livery Designs
            </h1>

            {/* Subtitle - Reduced Spacing */}
            <p className="text-base md:text-lg lg:text-xl text-gray-300 mb-8 max-w-2xl mx-auto font-light leading-relaxed animate-float-in delay-200">
              Professional automotive livery designs crafted with precision and creativity
            </p>

            {/* Simplified Button Layout */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-float-in delay-400">
              {/* Primary Button - View Gallery */}
              <button
                onClick={handleSeeMoreClick}
                className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-400 hover:to-orange-500 text-black font-bold rounded-xl transform hover:scale-105 transition-all duration-300"
                tabIndex={0}
                aria-label="See more livery designs"
              >
                VIEW GALLERY
                <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
              </button>

              {/* Secondary Button - Request Livery */}
              <Link
                href="/request-livery"
                className="inline-flex items-center px-8 py-3 border-2 border-yellow-500 hover:bg-yellow-500 hover:text-black text-yellow-500 font-bold rounded-xl transform hover:scale-105 transition-all duration-300"
                tabIndex={0}
                aria-label="Request custom livery design"
              >
                REQUEST LIVERY
                <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Link>
            </div>
            </div>
          </div>

          {/* Simple Scroll Indicator */}
          <div className="absolute bottom-12 left-1/2 transform -translate-x-1/2 animate-bounce">
            <div className="w-5 h-8 border border-yellow-400/60 rounded-full flex justify-center">
              <div className="w-0.5 h-2 bg-yellow-400 rounded-full mt-1.5"></div>
            </div>
          </div>
        </section>

        {/* Modern Livery Showcase Section */}
        <section id="livery-showcase" className="py-20 px-6 bg-gradient-to-b from-black to-gray-900">
          <div className="max-w-7xl mx-auto">
            {/* Section Header */}
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                Livery Gallery
              </h2>
              <div className="w-24 h-1 bg-gradient-to-r from-yellow-400 to-orange-500 mx-auto mb-6"></div>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Explore our collection of custom livery designs crafted with precision and creativity
              </p>
            </div>

            {/* Modern Gallery */}
            <ModernGallery 
              items={galleryItems}
              className="mb-16"
            />

            {/* Call to Action */}
            <div className="text-center mt-16">
              <div className="inline-flex flex-col items-center p-8 bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-2xl border border-gray-700">
                <h3 className="text-2xl font-bold text-white mb-4">
                  Ready to Create Your Custom Livery?
                </h3>
                <p className="text-gray-300 mb-6 max-w-md">
                  Transform your vision into reality with our professional livery design services
                </p>
                <Link
                  href="/request-livery"
                  className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 text-black font-bold rounded-lg transform hover:scale-105 transition-all duration-300"
                  tabIndex={0}
                  aria-label="Start your custom livery project"
                >
                  Start Your Project
                  <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Services & Pricing Section */}
        <section id="services" className="py-20 px-6 bg-gradient-to-b from-gray-900 to-black">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                Services & Pricing
              </h2>
              <div className="w-24 h-1 bg-gradient-to-r from-yellow-400 to-orange-500 mx-auto mb-6"></div>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Professional CarX livery designs tailored to your vision
              </p>
            </div>

            {/* Pricing Cards */}
            <div className="grid md:grid-cols-2 gap-8 mb-16">
              {/* PC Livery Card */}
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 to-orange-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm border border-gray-700 rounded-2xl p-8 hover:border-yellow-500/50 transition-all duration-300">
                  <div className="text-center mb-6">
                    <h3 className="text-2xl font-bold text-white mb-2">PC Livery</h3>
                    <div className="text-4xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-2">
                      $35 USD
                    </div>
                    <p className="text-gray-400">Premium detailed design</p>
                  </div>
                  <ul className="space-y-3 mb-8">
                    <li className="flex items-center text-gray-300">
                      <svg className="w-5 h-5 text-yellow-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Up to 5000 layers available
                    </li>
                    <li className="flex items-center text-gray-300">
                      <svg className="w-5 h-5 text-yellow-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Intricate details & fine touches
                    </li>
                    <li className="flex items-center text-gray-300">
                      <svg className="w-5 h-5 text-yellow-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Blender liveries available
                    </li>
                    <li className="flex items-center text-gray-300">
                      <svg className="w-5 h-5 text-yellow-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Step-by-step updates
                    </li>
                  </ul>
                </div>
              </div>

              {/* Console Livery Card */}
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm border border-gray-700 rounded-2xl p-8 hover:border-blue-500/50 transition-all duration-300">
                  <div className="text-center mb-6">
                    <h3 className="text-2xl font-bold text-white mb-2">Console Livery</h3>
                    <div className="text-4xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent mb-2">
                      $20 USD
                    </div>
                    <p className="text-gray-400">Optimized for console</p>
                  </div>
                  <ul className="space-y-3 mb-8">
                    <li className="flex items-center text-gray-300">
                      <svg className="w-5 h-5 text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Maximized detail within limits
                    </li>
                    <li className="flex items-center text-gray-300">
                      <svg className="w-5 h-5 text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Layer count optimized
                    </li>
                    <li className="flex items-center text-gray-300">
                      <svg className="w-5 h-5 text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Custom design vision
                    </li>
                    <li className="flex items-center text-gray-300">
                      <svg className="w-5 h-5 text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Professional quality
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Process Section */}
        <section id="process" className="py-20 px-6 bg-gradient-to-b from-black to-gray-900">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                Our Process
              </h2>
              <div className="w-24 h-1 bg-gradient-to-r from-yellow-400 to-orange-500 mx-auto mb-6"></div>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                From concept to completion, we ensure every detail meets your vision
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {/* Step 1 */}
              <div className="text-center group">
                <div className="relative mb-6">
                  <div className="w-20 h-20 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-2xl font-bold text-black">1</span>
                  </div>
                  <div className="absolute top-10 left-1/2 w-full h-0.5 bg-gradient-to-r from-yellow-400/50 to-transparent hidden md:block"></div>
                </div>
                <h3 className="text-xl font-bold text-white mb-4">Order & Consultation</h3>
                <p className="text-gray-300 leading-relaxed">
                  Fill out our detailed form and schedule a call to discuss your design vision, car details, and preferences.
                </p>
              </div>

              {/* Step 2 */}
              <div className="text-center group">
                <div className="relative mb-6">
                  <div className="w-20 h-20 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-2xl font-bold text-black">2</span>
                  </div>
                  <div className="absolute top-10 left-1/2 w-full h-0.5 bg-gradient-to-r from-yellow-400/50 to-transparent hidden md:block"></div>
                </div>
                <h3 className="text-xl font-bold text-white mb-4">Design & Updates</h3>
                <p className="text-gray-300 leading-relaxed">
                  We create your livery step-by-step, sharing progress updates and incorporating your feedback throughout the process.
                </p>
              </div>

              {/* Step 3 */}
              <div className="text-center group">
                <div className="relative mb-6">
                  <div className="w-20 h-20 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-2xl font-bold text-black">3</span>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-4">Delivery & Freedom</h3>
                <p className="text-gray-300 leading-relaxed">
                  Receive your completed livery via code or Kino livery. You're free to share, modify, and use it however you like.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* About Section */}
        <section id="about" className="py-20 px-6 bg-gradient-to-b from-gray-900 to-black">
          <div className="max-w-7xl mx-auto text-center">
            <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
              About Tezz Designs
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-yellow-400 to-orange-500 mx-auto mb-12"></div>
            <div className="max-w-4xl mx-auto space-y-6">
              <p className="text-xl text-gray-300 leading-relaxed">
                Welcome to Tezz Designs! I'm passionate about creating stunning livery designs for CarX and other racing platforms.
                My goal is to bring your vision to life with artistic creativity and technical precision.
              </p>
              <p className="text-lg text-gray-400 leading-relaxed">
                I believe every livery should be unique. That's why I never copy designs exactly as they are - instead,
                I put my own creative touches on each concept while respecting the original inspiration. Whether you're on PC or console,
                I'll work within the platform's limitations to create something truly special that makes your vehicle stand out on the track.
              </p>
              <div className="grid md:grid-cols-3 gap-8 mt-12">
                <div className="text-center">
                  <div className="text-3xl font-bold text-yellow-400 mb-2">5000+</div>
                  <div className="text-gray-300">Layers Available (PC)</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-yellow-400 mb-2">100%</div>
                  <div className="text-gray-300">Original Designs</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-yellow-400 mb-2">24/7</div>
                  <div className="text-gray-300">Design Support</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section id="contact" className="py-20 px-6 bg-gradient-to-b from-black to-gray-900">
          <div className="max-w-7xl mx-auto text-center">
            <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
              Ready to Get Started?
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-yellow-400 to-orange-500 mx-auto mb-12"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
              Transform your vision into reality with professional CarX livery design
            </p>
            <div className="flex justify-center items-center space-x-6 text-gray-400">
              <a 
                href="#" 
                className="hover:text-yellow-400 transition-colors duration-300 flex items-center"
                tabIndex={0}
                aria-label="Visit our Instagram page"
              >
                <svg className="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.40z"/>
                </svg>
                Instagram
              </a>
              <a 
                href="#" 
                className="hover:text-yellow-400 transition-colors duration-300 flex items-center"
                tabIndex={0}
                aria-label="Visit our Facebook page"
              >
                <svg className="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
                Facebook
              </a>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-black py-12 px-6 border-t border-gray-800 pb-24">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="flex items-center mb-6 md:mb-0">
                <Logo size="medium" showText={true} />
              </div>
              
              <div className="flex items-center space-x-6 text-gray-400">
                <a 
                  href="#" 
                  className="hover:text-yellow-400 transition-colors duration-300"
                  tabIndex={0}
                  aria-label="Visit our Instagram page"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.40z"/>
                  </svg>
                </a>
                <a 
                  href="#" 
                  className="hover:text-yellow-400 transition-colors duration-300"
                  tabIndex={0}
                  aria-label="Visit our Facebook page"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </a>
                <a 
                  href="#" 
                  className="hover:text-yellow-400 transition-colors duration-300"
                  tabIndex={0}
                  aria-label="Visit our Pinterest page"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-12.013C24.007 5.367 18.641.001 12.017.001z"/>
                  </svg>
                </a>
              </div>
            </div>
            
            <div className="border-t border-gray-800 mt-8 pt-8 text-center">
              <p className="text-gray-500 text-sm">
                © 2025 Tezz Designs. All rights reserved. | Professional automotive livery design services
              </p>
            </div>
          </div>
        </footer>

        {/* Dock Navigation - Fixed at bottom */}
        <div className="fixed bottom-0 left-0 right-0 z-50 pointer-events-none">
          <div className="pointer-events-auto">
            <Dock 
              items={dockItems}
              panelHeight={isMobile ? 60 : 68}
              baseItemSize={isMobile ? 44 : 50}
              magnification={isMobile ? 60 : 70}
              distance={isMobile ? 150 : 200}
            />
          </div>
        </div>
      </main>
    </>
  );
  };

export default HomePage; 